import 'package:flutter/material.dart';
import 'package:awesome_notifications_service/awesome_notifications_service.dart';
import 'package:flutter/foundation.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize the notification service
  final initialized = await AwesomeNotificationsService.initialize();
  if (!initialized) {
    if (kDebugMode) {
      print('Failed to initialize notification service');
    }
    return;
  }

  runApp(const NotificationTestApp());
}

class NotificationTestApp extends StatelessWidget {
  const NotificationTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Awesome Notifications Service Test',
      theme: ThemeData(
        primarySwatch: Colors.green,
        useMaterial3: true,
      ),
      home: const NotificationTestHomePage(),
    );
  }
}

class NotificationTestHomePage extends StatefulWidget {
  const NotificationTestHomePage({super.key});

  @override
  State<NotificationTestHomePage> createState() =>
      _NotificationTestHomePageState();
}

class _NotificationTestHomePageState extends State<NotificationTestHomePage> {
  bool _hasPermissions = false;
  NotificationStatistics? _stats;
  String _statusMessage = 'Checking permissions...';

  @override
  void initState() {
    super.initState();
    _initializeAndCheck();
  }

  Future<void> _initializeAndCheck() async {
    await _checkPermissions();
    await _loadStats();
  }

  Future<void> _checkPermissions() async {
    try {
      final status = await AwesomeNotificationsService.getPermissionStatus();
      setState(() {
        _hasPermissions = status.hasAllPermissions;
        _statusMessage = status.hasAllPermissions
            ? 'All permissions granted'
            : 'Permissions needed: ${_getMissingPermissions(status)}';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error checking permissions: $e';
      });
    }
  }

  String _getMissingPermissions(PermissionStatus status) {
    final missing = <String>[];
    if (!status.hasNotificationPermission) missing.add('Notifications');
    if (!status.hasExactAlarmPermission) missing.add('Exact Alarms');
    if (!status.hasCriticalAlertPermission) missing.add('Critical Alerts');
    return missing.join(', ');
  }

  Future<void> _requestPermissions() async {
    try {
      final granted = await AwesomeNotificationsService.requestPermissions();

      // Check specific permission status for better feedback
      final status = await AwesomeNotificationsService.getPermissionStatus();

      setState(() {
        _hasPermissions = granted;
        if (granted) {
          _statusMessage = 'All permissions granted successfully';
        } else {
          final missing = _getMissingPermissions(status);
          _statusMessage = 'Some permissions were denied: $missing';

          // Show specific guidance for exact alarm permission
          if (!status.hasExactAlarmPermission) {
            _showExactAlarmPermissionDialog();
          }
        }
      });

      if (!granted) {
        await AwesomeNotificationsService.openNotificationSettings();
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Error requesting permissions: $e';
      });
    }
  }

  void _showExactAlarmPermissionDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Exact Alarm Permission'),
        content: SingleChildScrollView(
          child: Text(
            AwesomeNotificationsService.getExactAlarmPermissionExplanation(),
            style: const TextStyle(fontSize: 14),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Later'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await AwesomeNotificationsService.openExactAlarmSettings();
              // Recheck permissions after user returns
              await Future.delayed(const Duration(seconds: 1));
              await _checkPermissions();
            },
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );
  }

  Future<void> _loadStats() async {
    final stats = await AwesomeNotificationsService.getNotificationStatistics();
    setState(() {
      _stats = stats;
    });
  }

  void _showResult(String operation, bool success, [String? additionalInfo]) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            '$operation: ${success ? 'Success' : 'Failed'}'
            '${additionalInfo != null ? '\n$additionalInfo' : ''}',
          ),
          backgroundColor: success ? Colors.green : Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  void _showError(String operation, String error) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('$operation Error: $error'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  Future<void> _testPrayerNotifications() async {
    try {
      final now = DateTime.now();
      // Schedule prayer times for today and tomorrow for testing
      final prayerTimes = PrayerTimes(
        fajr: DateTime(now.year, now.month, now.day + 1, 5, 30),
        sunrise: DateTime(now.year, now.month, now.day + 1, 7, 0),
        dhuhr: DateTime(now.year, now.month, now.day + 1, 12, 15),
        asr: DateTime(now.year, now.month, now.day + 1, 15, 30),
        maghrib: DateTime(now.year, now.month, now.day + 1, 18, 0),
        isha: DateTime(now.year, now.month, now.day + 1, 19, 30),
      );

      final notificationConfigs = {
        'Fajr': PrayerNotificationConfig(
          prayerName: 'Fajr',
          isNotified: true,
          isPreNotified: true,
          soundIndex: 0,
        ),
        'Sunrise': PrayerNotificationConfig(
          prayerName: 'Sunrise',
          isNotified: true,
          isPreNotified: false,
          soundIndex: 4, // Bird sound
        ),
        'Dhuhr': PrayerNotificationConfig(
          prayerName: 'Dhuhr',
          isNotified: true,
          isPreNotified: false,
          soundIndex: 1,
        ),
        'Asr': PrayerNotificationConfig(
          prayerName: 'Asr',
          isNotified: true,
          isPreNotified: false,
          soundIndex: 2,
        ),
        'Maghrib': PrayerNotificationConfig(
          prayerName: 'Maghrib',
          isNotified: true,
          isPreNotified: true,
          iqamahMinutes: 5,
          soundIndex: 3,
        ),
        'Isha': PrayerNotificationConfig(
          prayerName: 'Isha',
          isNotified: true,
          isPreNotified: false,
          soundIndex: 0,
        ),
      };

      final success =
          await AwesomeNotificationsService.schedulePrayerNotifications(
        prayerTimes: prayerTimes,
        notificationConfigs: notificationConfigs,
        daysAhead: 2, // Test with 2 days
        locale: 'ar',
      );

      _showResult('Prayer Notifications', success);
      await _loadStats();
    } catch (e) {
      _showError('Prayer Notifications', e.toString());
    }
  }

  Future<void> _testImmediatePrayerNotification() async {
    try {
      final now = DateTime.now();
      final testTime =
          now.add(const Duration(seconds: 5)); // 5 seconds from now

      final prayerTimes = PrayerTimes(
        fajr: testTime,
        sunrise: testTime.add(const Duration(minutes: 1)),
        dhuhr: testTime.add(const Duration(minutes: 2)),
        asr: testTime.add(const Duration(minutes: 3)),
        maghrib: testTime.add(const Duration(minutes: 4)),
        isha: testTime.add(const Duration(minutes: 5)),
      );

      final notificationConfigs = {
        'Fajr': PrayerNotificationConfig(
          prayerName: 'Fajr',
          isNotified: true,
          isPreNotified: false,
          soundIndex: 0,
        ),
      };

      final success =
          await AwesomeNotificationsService.schedulePrayerNotifications(
        prayerTimes: prayerTimes,
        notificationConfigs: notificationConfigs,
        daysAhead: 1,
        locale: 'ar',
      );

      _showResult('Immediate Prayer Test', success,
          'Test notification will appear in 5 seconds');
      await _loadStats();
    } catch (e) {
      _showError('Immediate Prayer Test', e.toString());
    }
  }

  Future<void> _testMorningAthkar() async {
    try {
      final now = DateTime.now();
      final testTime =
          now.add(const Duration(seconds: 10)); // 10 seconds from now
      final timeString =
          '${testTime.hour.toString().padLeft(2, '0')}:${testTime.minute.toString().padLeft(2, '0')}';

      final config = AthkarNotificationConfig(
        type: AthkarType.morning,
        isEnabled: true,
        time: timeString,
      );

      final success = await AwesomeNotificationsService.scheduleMorningAthkar(
        config: config,
        daysAhead: 1,
        locale: 'ar',
      );

      _showResult('Morning Athkar Test', success,
          'Test notification will appear in 10 seconds');
      await _loadStats();
    } catch (e) {
      _showError('Morning Athkar Test', e.toString());
    }
  }

  Future<void> _testEveningAthkar() async {
    try {
      final now = DateTime.now();
      final testTime =
          now.add(const Duration(seconds: 15)); // 15 seconds from now
      final timeString =
          '${testTime.hour.toString().padLeft(2, '0')}:${testTime.minute.toString().padLeft(2, '0')}';

      final config = AthkarNotificationConfig(
        type: AthkarType.evening,
        isEnabled: true,
        time: timeString,
      );

      final success = await AwesomeNotificationsService.scheduleEveningAthkar(
        config: config,
        daysAhead: 1,
        locale: 'ar',
      );

      _showResult('Evening Athkar Test', success,
          'Test notification will appear in 15 seconds');
      await _loadStats();
    } catch (e) {
      _showError('Evening Athkar Test', e.toString());
    }
  }

  Future<void> _testDhikrReminders() async {
    try {
      final now = DateTime.now();
      final testTime =
          now.add(const Duration(seconds: 20)); // 20 seconds from now
      final timeString =
          '${testTime.hour.toString().padLeft(2, '0')}:${testTime.minute.toString().padLeft(2, '0')}';

      final config = AthkarNotificationConfig(
        type: AthkarType.dhikr,
        isEnabled: true,
        time: timeString,
        dhikrItems:
            PredefinedDhikr.items.take(3).toList(), // Test with first 3 items
        intervalMinutes: 1, // 1 minute interval for testing
      );

      final success = await AwesomeNotificationsService.scheduleDhikrReminders(
        config: config,
        daysAhead: 1,
        locale: 'ar',
      );

      _showResult('Dhikr Reminders Test', success,
          'Test notifications will start in 20 seconds');
      await _loadStats();
    } catch (e) {
      _showError('Dhikr Reminders Test', e.toString());
    }
  }

  Future<void> _testCustomSoundNotifications() async {
    try {
      final now = DateTime.now();

      // Test 1: Prayer notification with custom bird sound (asset-based)
      final testTime1 = now.add(const Duration(seconds: 5));
      final prayerTimes = PrayerTimes(
        fajr: testTime1,
        sunrise: testTime1.add(const Duration(minutes: 1)),
        dhuhr: testTime1.add(const Duration(minutes: 2)),
        asr: testTime1.add(const Duration(minutes: 3)),
        maghrib: testTime1.add(const Duration(minutes: 4)),
        isha: testTime1.add(const Duration(minutes: 5)),
      );

      // Configure notification with custom sound (bird sound from assets)
      final notificationConfigs = {
        'Fajr': PrayerNotificationConfig(
          prayerName: 'Fajr',
          isNotified: true,
          isPreNotified: false,
          soundIndex: 4, // Bird sound index - uses assets/sounds/bird.mp3
          useDefaultSound: false,
        ),
      };

      final success1 =
          await AwesomeNotificationsService.schedulePrayerNotifications(
        prayerTimes: prayerTimes,
        notificationConfigs: notificationConfigs,
        daysAhead: 1,
        locale: 'en',
      );

      _showResult('Custom Sound Prayer Test', success1,
          'Custom bird sound notification will appear in 5 seconds');
      await _loadStats();
    } catch (e) {
      _showError('Custom Sound Test', e.toString());
    }
  }

  Future<void> _cancelAllNotifications() async {
    final success = await AwesomeNotificationsService.cancelAllNotifications();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success
              ? 'All notifications cancelled'
              : 'Failed to cancel notifications'),
        ),
      );
    }

    await _loadStats();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Awesome Notifications Test'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Permission status
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      'Permissions Status',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _statusMessage,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: _hasPermissions ? Colors.green : Colors.orange,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (!_hasPermissions) ...[
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: _requestPermissions,
                        child: const Text('Request Permissions'),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () async {
                          await AwesomeNotificationsService
                              .openExactAlarmSettings();
                          await Future.delayed(const Duration(seconds: 1));
                          await _checkPermissions();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Fix Exact Alarm Permission'),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Statistics
            if (_stats != null)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        'Notification Statistics',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Text('Total: ${_stats!.totalNotifications}'),
                      Text('Prayer: ${_stats!.prayerNotifications}'),
                      Text('Athkar: ${_stats!.athkarNotifications}'),
                      Text('Firebase: ${_stats!.firebaseNotifications}'),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 16),

            // Test section header
            Text(
              'Notification Tests',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Prayer notification tests
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      'Prayer Notifications',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed:
                          _hasPermissions ? _testPrayerNotifications : null,
                      child: const Text('Test Prayer Schedule (Tomorrow)'),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _hasPermissions
                          ? _testImmediatePrayerNotification
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Test Immediate Prayer (5s)'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Athkar notification tests
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      'Athkar Notifications',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _hasPermissions ? _testMorningAthkar : null,
                      child: const Text('Test Morning Athkar (10s)'),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _hasPermissions ? _testEveningAthkar : null,
                      child: const Text('Test Evening Athkar (15s)'),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _hasPermissions ? _testDhikrReminders : null,
                      child: const Text('Test Dhikr Reminders (20s)'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Custom sound notification tests
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      'Custom Sound Notifications',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _hasPermissions
                          ? _testCustomSoundNotifications
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.purple,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Test Custom Sound (5s)'),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'This test demonstrates how to use custom notification sounds from asset files. '
                      'It will play a bird sound from assets/sounds/bird.mp3',
                      style: TextStyle(fontSize: 12, color: Colors.grey),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      'Custom Sound Notifications',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _hasPermissions
                          ? _testCustomSoundNotifications
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.purple,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Test Custom Sound (5s)'),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'This test demonstrates how to use custom notification sounds from asset files. '
                      'It will play a bird sound from assets/sounds/bird.mp3',
                      style: TextStyle(fontSize: 12, color: Colors.grey),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Control buttons
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      'Controls',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _cancelAllNotifications,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Cancel All Notifications'),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _loadStats,
                      child: const Text('Refresh Statistics'),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Instructions
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Test Instructions:',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    const Text('1. First, request permissions if not granted'),
                    const Text(
                        '2. Use immediate tests to see notifications quickly'),
                    const Text(
                        '3. Check statistics to see scheduled notifications'),
                    const Text(
                        '4. Use cancel button to clear all notifications'),
                    const Text(
                        '5. Prayer schedule test sets notifications for tomorrow'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
