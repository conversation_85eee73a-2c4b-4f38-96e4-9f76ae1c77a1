// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:awesome_notifications_service/awesome_notifications_service.dart';

import 'package:awesome_notifications_service_example/main.dart';

void main() {
  testWidgets('Notification service test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const NotificationTestApp());

    // Verify that the app loads correctly
    expect(find.text('Awesome Notifications Service Test'), findsOneWidget);
  });

  group('Custom Notification Sounds Tests', () {
    testWidgets('Test custom sound notification creation',
        (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const NotificationTestApp());

      // Wait for the app to initialize
      await tester.pumpAndSettle();

      // Verify the app is loaded
      expect(find.text('Awesome Notifications Service Test'), findsOneWidget);

      // Test will verify the UI is ready for custom sound notifications
      // The actual sound testing requires device-specific testing
    });

    test('Custom sound path generation test', () {
      // Test different sound path formats that the package supports

      // Test 1: Asset-based sound path (Flutter assets)
      const assetSoundPath = 'assets/sounds/bird.mp3';
      expect(assetSoundPath.contains('assets/sounds/'), isTrue);
      expect(assetSoundPath.endsWith('.mp3'), isTrue);

      // Test 2: Resource-based sound path (Android native resources)
      const resourceSoundPath = 'resource://raw/bird';
      expect(resourceSoundPath.startsWith('resource://raw/'), isTrue);

      // Test 3: Validate sound file extensions
      const validExtensions = ['.mp3', '.wav', '.ogg'];
      for (String ext in validExtensions) {
        final soundPath = 'assets/sounds/test_sound$ext';
        expect(validExtensions.any((e) => soundPath.endsWith(e)), isTrue);
      }
    });

    test('Prayer notification with custom sound configuration', () {
      // Test prayer notification configuration with custom sounds
      final prayerConfig = PrayerNotificationConfig(
        prayerName: 'Fajr',
        isNotified: true,
        isPreNotified: false,
        soundIndex: 4, // Bird sound index
        useDefaultSound: false,
      );

      expect(prayerConfig.prayerName, equals('Fajr'));
      expect(prayerConfig.isNotified, isTrue);
      expect(prayerConfig.soundIndex, equals(4));
      expect(prayerConfig.useDefaultSound, isFalse);
    });

    test('Athkar notification with custom sound configuration', () {
      // Test athkar notification configuration
      final athkarConfig = AthkarNotificationConfig(
        type: AthkarType.morning,
        isEnabled: true,
        time: '06:00',
      );

      expect(athkarConfig.type, equals(AthkarType.morning));
      expect(athkarConfig.isEnabled, isTrue);
      expect(athkarConfig.time, equals('06:00'));
    });
  });

  group('Sound File Asset Management Tests', () {
    test('Asset sound file path validation', () {
      // Test various asset path formats
      const testPaths = [
        'assets/sounds/bird.mp3',
        'assets/sounds/custom_athan.wav',
        'assets/sounds/notification_tone.ogg',
      ];

      for (String path in testPaths) {
        expect(path.startsWith('assets/sounds/'), isTrue);
        expect(path.contains('.'), isTrue);
      }
    });

    test('Resource sound file path validation', () {
      // Test resource path formats used by the package
      const testResourcePaths = [
        'resource://raw/bird',
        'resource://raw/athan1',
        'resource://raw/custom_sound',
      ];

      for (String path in testResourcePaths) {
        expect(path.startsWith('resource://raw/'), isTrue);
      }
    });

    test('Sound file extension validation', () {
      // Test supported audio file extensions
      const supportedExtensions = ['.mp3', '.wav', '.ogg', '.m4a'];
      const testFiles = [
        'bird.mp3',
        'athan.wav',
        'notification.ogg',
        'alert.m4a',
      ];

      for (String file in testFiles) {
        final hasValidExtension =
            supportedExtensions.any((ext) => file.endsWith(ext));
        expect(hasValidExtension, isTrue);
      }
    });
  });
}
